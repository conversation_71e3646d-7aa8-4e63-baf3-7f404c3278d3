package com.seera.lumi.yaqeen.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentUploadService {

  private final RestTemplate restTemplate;

  @Value("${api.fleet.base.url:https://api.lumirental.com/core-fleet-service}")
  private String fleetServiceBaseUrl;

  @Value("${api.fleet.auth.token:}")
  private String authToken;

  /**
   * Upload multiple files using the existing fleet service document upload endpoint
   * @param plateNo Vehicle plate number
   * @param files Map of filename to File objects
   * @return List of upload responses/URLs
   */
  public List<String> uploadFiles(String plateNo, Map<String, File> files) {
    List<String> responses = new ArrayList<>();
    
    if (authToken.isEmpty()) {
      log.warn("Fleet service auth token is not configured. Cannot upload files.");
      return responses;
    }

    for (Map.Entry<String, File> entry : files.entrySet()) {
      try {
        String response = uploadFile(plateNo, entry.getValue(), entry.getKey());
        if (response != null) {
          responses.add(response);
        }
      } catch (Exception e) {
        log.error("Failed to upload file {}: {}", entry.getKey(), e.getMessage(), e);
      }
    }

    return responses;
  }

  /**
   * Upload a single file to the fleet service document upload endpoint
   * @param plateNo Vehicle plate number
   * @param file File to upload
   * @param originalFileName Original filename
   * @return Upload response or null if failed
   */
  public String uploadFile(String plateNo, File file, String originalFileName) {
    if (authToken.isEmpty()) {
      log.warn("Fleet service auth token is not configured. Cannot upload file: {}", originalFileName);
      return null;
    }

    try {
      String url = fleetServiceBaseUrl + "/v1/vehicles/documents/upload";
      
      // Prepare headers
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.MULTIPART_FORM_DATA);
      headers.setBearerAuth(authToken);

      // Prepare multipart body
      MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
      body.add("plateNo", plateNo);
      body.add("file", new FileSystemResource(file) {
        @Override
        public String getFilename() {
          return originalFileName; // Use original filename
        }
      });

      HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

      // Make the request
      ResponseEntity<Object> response = restTemplate.exchange(
          url,
          HttpMethod.POST,
          requestEntity,
          Object.class
      );

      if (response.getStatusCode().is2xxSuccessful()) {
        log.info("Successfully uploaded file {} for plate {}: {}", originalFileName, plateNo, response.getBody());
        return response.getBody().toString();
      } else {
        log.error("Failed to upload file {} for plate {}. Status: {}", originalFileName, plateNo, response.getStatusCode());
        return null;
      }

    } catch (Exception e) {
      log.error("Error uploading file {} for plate {} to fleet service: {}", originalFileName, plateNo, e.getMessage(), e);
      return null;
    }
  }

  /**
   * Check if document upload service is properly configured
   */
  public boolean isConfigured() {
    return !authToken.isEmpty() && !fleetServiceBaseUrl.isEmpty();
  }

  /**
   * Upload files with a default plate number for reports
   * @param files Map of filename to File objects
   * @return List of upload responses/URLs
   */
  public List<String> uploadReportFiles(Map<String, File> files) {
    // Use a default plate number for report uploads
    String defaultPlateNo = "REPORT";
    return uploadFiles(defaultPlateNo, files);
  }
}
