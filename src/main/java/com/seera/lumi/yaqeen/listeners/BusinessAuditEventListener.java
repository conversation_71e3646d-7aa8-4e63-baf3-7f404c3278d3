package com.seera.lumi.yaqeen.listeners;

import com.seera.lumi.yaqeen.dto.BusinessAuditLogInfo;
import com.seera.lumi.yaqeen.service.BusinessAuditEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessAuditEventListener {

  private final BusinessAuditEventService businessAuditEventService;

  @KafkaListener(
      topics = {"${kafka.topic.business.audit.events:business_audit_events}"},
      groupId = "business-audit-event-listener",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void handleBusinessAuditEvent(@Payload BusinessAuditLogInfo event) {
    try {
      log.info("Received business audit event message from main cluster: {}", event);
      businessAuditEventService.processBusinessAuditEvent(event);
      log.info("Successfully processed business audit event with auditId: {}", event.getAuditId());
    } catch (Exception e) {
      log.error("Error processing business audit event message from main cluster: {}", event, e);
    }
  }

  @KafkaListener(
      topics = {"${kafka.topic.business.audit.events.default:business_audit_events}"},
      groupId = "business-audit-event-listener-default",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "defaultKafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void handleBusinessAuditEventFromDefaultCluster(@Payload BusinessAuditLogInfo event) {
    try {
      log.info("Received business audit event message from default cluster: {}", event);
      businessAuditEventService.processBusinessAuditEvent(event);
      log.info("Successfully processed business audit event from default cluster with auditId: {}", event.getAuditId());
    } catch (Exception e) {
      log.error("Error processing business audit event message from default cluster: {}", event, e);
    }
  }
}
