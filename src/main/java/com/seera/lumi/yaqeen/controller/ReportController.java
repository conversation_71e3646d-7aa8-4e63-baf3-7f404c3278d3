package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.reports.ReportScheduler;
import com.seera.lumi.yaqeen.dto.CustomReportRequest;

import lombok.RequiredArgsConstructor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Component
@RestController
@RequestMapping("/api/reports")
@RequiredArgsConstructor
public class ReportController {

  private final Logger log = LoggerFactory.getLogger(ReportController.class);

  private final ReportScheduler reportScheduler;

  @GetMapping("/generate-all-reports")
  public String generateAllReports() {
    reportScheduler.generateAllReports();
    return "All Reports triggered successfully!";
  }

  @GetMapping("/generate-traffic-fine-reports")
  public String generateTrafficFineReports() {
    reportScheduler.generateTrafficFineReports();
    return "Traffic Fine Reports triggered successfully!";
  }

  @PostMapping("/custom-email-reports")
  public ResponseEntity<?> generateCustomEmailReports(
      @RequestBody CustomReportRequest request,
      @RequestHeader(value = "X-OPS-API-KEY", required = false) String apiKey,
      @RequestParam(value = "api_key", required = false) String apiKeyParam) {
    try {
      // Security validation - check for API key in header or request parameter
      if (!isValidApiKey(apiKey, apiKeyParam)) {
        log.error("Unauthorized access attempt to /custom-email-reports endpoint");
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(Map.of("error", "Unauthorized: Valid API key required"));
      }

      reportScheduler.generateAndSendCustomReports(request);
      return ResponseEntity.ok(
          Map.of("success", true, "message", "Custom reports generated and email triggered!"));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(Map.of("error", "Failed to generate reports: " + e.getMessage()));
    }
  }

  /**
   * Validates the API key from either header or request parameter
   *
   * @param headerApiKey API key from X-OPS-API-KEY header
   * @param paramApiKey API key from api_key request parameter
   * @return true if valid API key is provided, false otherwise
   */
  private boolean isValidApiKey(String headerApiKey, String paramApiKey) {
    // Check if API key is provided in header
    if (headerApiKey != null && !headerApiKey.trim().isEmpty()) {
      return "X9m2Q4p8L7r1T6v3C5z0N8k2H4y7J1w9".equals(headerApiKey.trim());
    }

    // Check if API key is provided in request parameter
    if (paramApiKey != null && !paramApiKey.trim().isEmpty()) {
      return "f1e2d3c4b5a697887766554433221100".equals(paramApiKey.trim());
    }

    // No valid API key found
    return false;
  }
}
