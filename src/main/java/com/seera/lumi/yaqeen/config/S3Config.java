package com.seera.lumi.yaqeen.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

@Slf4j
@Configuration
public class S3Config {

  @Value("${aws.s3.access-key:}")
  private String accessKey;

  @Value("${aws.s3.secret-key:}")
  private String secretKey;

  @Value("${aws.s3.region:us-east-1}")
  private String region;

  @Bean
  public S3Client s3Client() {
    try {
      log.info("Initializing S3 client for region: {}", region);
      
      if (accessKey.isEmpty() || secretKey.isEmpty()) {
        log.warn("AWS credentials not provided. S3 functionality will be disabled.");
        return null;
      }

      AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);
      
      S3Client s3Client = S3Client.builder()
          .region(Region.of(region))
          .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
          .build();

      log.info("S3 client initialized successfully");
      return s3Client;
    } catch (Exception e) {
      log.error("Failed to initialize S3 client: {}", e.getMessage(), e);
      return null;
    }
  }
}
