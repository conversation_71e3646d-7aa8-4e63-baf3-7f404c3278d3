package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.service.S3Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/s3")
@RequiredArgsConstructor
@Tag(name = "S3 File Upload", description = "APIs for uploading files to S3")
public class S3Controller {

  private final S3Service s3Service;

  @PostMapping("/upload")
  @Operation(summary = "Upload files to S3", description = "Upload multiple files to S3 and get their URLs")
  @ApiResponses(value = {
      @ApiResponse(responseCode = "200", description = "Files uploaded successfully"),
      @ApiResponse(responseCode = "400", description = "Bad request"),
      @ApiResponse(responseCode = "500", description = "Internal server error")
  })
  public ResponseEntity<Map<String, Object>> uploadFiles(
      @Parameter(description = "Files to upload", required = true)
      @RequestParam("files") MultipartFile[] files) {
    
    Map<String, Object> response = new HashMap<>();
    
    try {
      if (!s3Service.isConfigured()) {
        response.put("success", false);
        response.put("message", "S3 service is not configured");
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
      }

      if (files == null || files.length == 0) {
        response.put("success", false);
        response.put("message", "No files provided");
        return ResponseEntity.badRequest().body(response);
      }

      Map<String, File> tempFiles = new HashMap<>();
      List<String> urls = new ArrayList<>();

      try {
        // Convert MultipartFiles to temporary Files
        for (MultipartFile multipartFile : files) {
          if (!multipartFile.isEmpty()) {
            File tempFile = convertMultipartFileToFile(multipartFile);
            tempFiles.put(multipartFile.getOriginalFilename(), tempFile);
          }
        }

        // Upload files to S3
        urls = s3Service.uploadFiles(tempFiles);

        response.put("success", true);
        response.put("message", "Files uploaded successfully");
        response.put("urls", urls);
        response.put("uploadedCount", urls.size());
        response.put("totalFiles", files.length);

        log.info("Successfully uploaded {} out of {} files to S3", urls.size(), files.length);
        
        return ResponseEntity.ok(response);

      } finally {
        // Clean up temporary files
        for (File tempFile : tempFiles.values()) {
          try {
            if (tempFile.exists()) {
              tempFile.delete();
            }
          } catch (Exception e) {
            log.warn("Failed to delete temporary file: {}", tempFile.getName());
          }
        }
      }

    } catch (Exception e) {
      log.error("Error uploading files to S3: {}", e.getMessage(), e);
      response.put("success", false);
      response.put("message", "Failed to upload files: " + e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  @PostMapping("/upload-single")
  @Operation(summary = "Upload single file to S3", description = "Upload a single file to S3 and get its URL")
  @ApiResponses(value = {
      @ApiResponse(responseCode = "200", description = "File uploaded successfully"),
      @ApiResponse(responseCode = "400", description = "Bad request"),
      @ApiResponse(responseCode = "500", description = "Internal server error")
  })
  public ResponseEntity<Map<String, Object>> uploadSingleFile(
      @Parameter(description = "File to upload", required = true)
      @RequestParam("file") MultipartFile file) {
    
    Map<String, Object> response = new HashMap<>();
    
    try {
      if (!s3Service.isConfigured()) {
        response.put("success", false);
        response.put("message", "S3 service is not configured");
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
      }

      if (file == null || file.isEmpty()) {
        response.put("success", false);
        response.put("message", "No file provided");
        return ResponseEntity.badRequest().body(response);
      }

      File tempFile = null;
      try {
        // Convert MultipartFile to temporary File
        tempFile = convertMultipartFileToFile(file);

        // Upload file to S3
        String url = s3Service.uploadFile(file.getOriginalFilename(), tempFile);

        if (url != null) {
          response.put("success", true);
          response.put("message", "File uploaded successfully");
          response.put("url", url);
          response.put("fileName", file.getOriginalFilename());
          
          log.info("Successfully uploaded file {} to S3: {}", file.getOriginalFilename(), url);
          return ResponseEntity.ok(response);
        } else {
          response.put("success", false);
          response.put("message", "Failed to upload file to S3");
          return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }

      } finally {
        // Clean up temporary file
        if (tempFile != null && tempFile.exists()) {
          try {
            tempFile.delete();
          } catch (Exception e) {
            log.warn("Failed to delete temporary file: {}", tempFile.getName());
          }
        }
      }

    } catch (Exception e) {
      log.error("Error uploading file to S3: {}", e.getMessage(), e);
      response.put("success", false);
      response.put("message", "Failed to upload file: " + e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  @GetMapping("/status")
  @Operation(summary = "Check S3 service status", description = "Check if S3 service is properly configured")
  public ResponseEntity<Map<String, Object>> getS3Status() {
    Map<String, Object> response = new HashMap<>();
    
    boolean isConfigured = s3Service.isConfigured();
    response.put("configured", isConfigured);
    response.put("message", isConfigured ? "S3 service is configured and ready" : "S3 service is not configured");
    
    return ResponseEntity.ok(response);
  }

  /**
   * Convert MultipartFile to File
   */
  private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
    String originalFilename = multipartFile.getOriginalFilename();
    if (originalFilename == null) {
      originalFilename = "temp_file";
    }
    
    // Create temporary file
    Path tempPath = Files.createTempFile("upload_", "_" + originalFilename);
    File tempFile = tempPath.toFile();
    
    // Copy multipart file content to temporary file
    Files.copy(multipartFile.getInputStream(), tempPath, StandardCopyOption.REPLACE_EXISTING);
    
    return tempFile;
  }
}
