package com.seera.lumi.yaqeen.domain;

import com.seera.lumi.yaqeen.domain.converter.DotDateConverter;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.EqualsAndHashCode;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "maintenance")
@EqualsAndHashCode(callSuper = true)
public class Maintenance extends BaseEntity {

    @Column(name = "doc_date", nullable = false)
    @Convert(converter = DotDateConverter.class)
    private LocalDateTime documentDate;

    @Column(name = "plate_2", nullable = false, length = 45)
    private String plateNo;

    @Column(name = "Local_currency_amount", nullable = false, length = 12)
    private String amount;

    @Column(name = "text", length = 255)
    private String text;

    @Column(name = "km", length = 45)
    private String km;

    @Column(name = "service_type", length = 45)
    private String serviceType;

    @Column(name = "gl_no", length = 45)
    private String glNo;

    @Column(name = "posting_date", length = 45)
    private String postingDate;
}
