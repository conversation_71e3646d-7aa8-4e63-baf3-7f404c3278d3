package com.seera.lumi.yaqeen.domain.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * JPA converter to handle date strings in 'yyyy.MM.dd' format from database
 * and convert them to LocalDateTime for the entity.
 */
@Slf4j
@Converter
public class DotDateConverter implements AttributeConverter<LocalDateTime, String> {

    private static final DateTimeFormatter DOT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    @Override
    public String convertToDatabaseColumn(LocalDateTime attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.toLocalDate().format(DOT_DATE_FORMATTER);
    }

    @Override
    public LocalDateTime convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        try {
            // Parse the date string in 'yyyy.MM.dd' format and convert to LocalDateTime at start of day
            LocalDate date = LocalDate.parse(dbData.trim(), DOT_DATE_FORMATTER);
            return date.atStartOfDay();
        } catch (DateTimeParseException e) {
            log.error("Failed to parse date string '{}' with format 'yyyy.MM.dd': {}", dbData, e.getMessage());
            throw new RuntimeException("Cannot convert date string '" + dbData + "' to LocalDateTime", e);
        }
    }
}
