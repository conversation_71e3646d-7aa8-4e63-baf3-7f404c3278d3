package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.domain.Maintenance;
import com.seera.lumi.yaqeen.repository.MaintenanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/maintenance")
@RequiredArgsConstructor
public class MaintenanceController {

    private final MaintenanceRepository maintenanceRepository;

    /**
     * Get all maintenance logs for a specific plate number with query parameter
     * 
     * @param plateNo the plate number to search for
     * @return list of maintenance records for the given plate
     */
    @GetMapping("/search")
    public ResponseEntity<List<Maintenance>> searchMaintenanceByPlate(
        @RequestParam(required = true, name = "plateNo") String plateNo) {
        log.info("Searching maintenance records for plate: {}", plateNo);

        if (plateNo == null || plateNo.trim().isEmpty()) {
            log.error("Plate number cannot be empty");
            return ResponseEntity.badRequest().build();
        }

        try {
            List<Maintenance> maintenanceRecords = maintenanceRepository.findByPlateNoOrderByDocumentDateDesc(plateNo);
            log.info("Found {} maintenance records for plate: {}", maintenanceRecords.size(), plateNo);
            return ResponseEntity.ok(maintenanceRecords);
        } catch (Exception e) {
            log.error("Error searching maintenance records for plate: {}", plateNo, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
