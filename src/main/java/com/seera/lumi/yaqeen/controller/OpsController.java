package com.seera.lumi.yaqeen.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/ops")
public class OpsController {

  private final DataSource dataSource;

  @GetMapping("/schema")
  public ResponseEntity<?> getDatabaseSchema(
      @RequestHeader(value = "X-OPS-API-KEY", required = false) String apiKey,
      @RequestParam(value = "api_key", required = false) String apiKeyParam) {
    try {
      // Security validation - check for API key in header or request parameter
      if (!isValidApiKey(apiKey, apiKeyParam)) {
        log.warn("Unauthorized access attempt to /schema endpoint");
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(Map.of("error", "Unauthorized: Valid API key required"));
      }
      SchemaResponse schemaResponse = extractSchemaResponse();
      return ResponseEntity.ok(
          Map.of(
              "success", true,
              "databases", schemaResponse.getDatabases(),
              "tables", schemaResponse.getTables(),
              "columns", schemaResponse.getColumns()));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(Map.of("error", "Failed to extract database schema: " + e.getMessage()));
    }
  }

  @PostMapping("/execute")
  public ResponseEntity<?> executeQuery(
      @RequestBody QueryRequest request,
      @RequestHeader(value = "X-OPS-API-KEY", required = false) String apiKey,
      @RequestParam(value = "api_key", required = false) String apiKeyParam) {
    try {
      // Security validation - check for API key in header or request parameter
      if (!isValidApiKey(apiKey, apiKeyParam)) {
        log.warn("Unauthorized access attempt to /execute endpoint");
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(Map.of("error", "Unauthorized: Valid API key required"));
      }
      // Validate if it's a SELECT query
      if (!isValidSelectQuery(request.getQuery())) {
        return ResponseEntity.badRequest()
            .body(Map.of("error", "Only SELECT queries are allowed for security reasons"));
      }

      // Add LIMIT 50 if not present
      String processedQuery = request.getQuery();

      // Execute the query
      QueryResult queryResult = executeSelectQuery(processedQuery);

      return ResponseEntity.ok(
          Map.of(
              "success", true,
              "columns", queryResult.getColumns(),
              "data", queryResult.getData(),
              "rowCount", queryResult.getData().size(),
              "originalQuery", request.getQuery(),
              "executedQuery", processedQuery));

    } catch (Exception e) {
      // log.error("Error executing query: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.BAD_REQUEST)
          .body(Map.of("error", "Failed to execute query: " + e.getMessage()));
    }
  }

  private boolean isValidSelectQuery(String query) {
    if (query == null || query.trim().isEmpty()) {
      return false;
    }

    String trimmedQuery = query.trim().toLowerCase();

    // Check if it starts with SELECT
    if (!trimmedQuery.startsWith("select ")) {
      return false;
    }

    // Check for dangerous keywords
    String[] dangerousKeywords = {
      "insert",
      "update",
      "delete",
      "drop",
      "create",
      "alter",
      "truncate",
      "grant",
      "revoke",
      "execute",
      "call"
    };

    for (String keyword : dangerousKeywords) {
      if (trimmedQuery.contains(keyword + " ")) {
        return false;
      }
    }

    return true;
  }

  private String addLimitIfMissing(String query) {
    if (query == null || query.trim().isEmpty()) {
      return query;
    }

    String trimmedQuery = query.trim();
    String lowerQuery = trimmedQuery.toLowerCase();

    // Check if LIMIT already exists
    if (lowerQuery.contains("limit ")) {
      return trimmedQuery;
    }

    // Add LIMIT 50 at the end
    return trimmedQuery + " LIMIT 50";
  }

  private SchemaResponse extractSchemaResponse() throws SQLException {
    SchemaResponse response = new SchemaResponse();

    // Only consider specific databases
    List<String> allowedDatabases =
        Arrays.asList(
            "lumi-core-agreement-service",
            "branch_service",
            "lumi-core-fleet-service",
            "booking_service",
            "payment_service",
            "lumi-core-user-service");

    try (Connection connection = dataSource.getConnection()) {
      DatabaseMetaData metaData = connection.getMetaData();

      // Get all available databases
      try (ResultSet databases = metaData.getCatalogs()) {
        while (databases.next()) {
          String databaseName = databases.getString("TABLE_CAT");

          // Only process allowed databases
          if (allowedDatabases.contains(databaseName)) {
            response.addDatabase(databaseName);

            // Get all tables in this database
            try (ResultSet tables =
                metaData.getTables(databaseName, null, "%", new String[] {"TABLE"})) {
              while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                String tableType = tables.getString("TABLE_TYPE");
                String tableComment = tables.getString("REMARKS");

                // Add table as formatted string with database context
                String tableInfo =
                    String.format(
                        "%s.%s (%s) - %s",
                        databaseName,
                        tableName,
                        tableType,
                        tableComment != null ? tableComment : "No comment");
                response.addTable(tableInfo);

                // Get columns for this table
                try (ResultSet columns = metaData.getColumns(databaseName, null, tableName, "%")) {
                  while (columns.next()) {
                    String columnName = columns.getString("COLUMN_NAME");
                    String dataType = columns.getString("TYPE_NAME");
                    int columnSize = columns.getInt("COLUMN_SIZE");
                    boolean isNullable = "YES".equals(columns.getString("IS_NULLABLE"));
                    String columnComment = columns.getString("REMARKS");

                    // Add column as formatted string with full context
                    String columnInfo =
                        String.format(
                            "%s.%s.%s (%s, size: %d, nullable: %s) - %s",
                            databaseName,
                            tableName,
                            columnName,
                            dataType,
                            columnSize,
                            isNullable,
                            columnComment != null ? columnComment : "No comment");
                    response.addColumn(columnInfo);
                  }
                }
              }
            }
          }
        }
      }
    }

    return response;
  }

  private QueryResult executeSelectQuery(String query) throws SQLException {
    List<Map<String, Object>> results = new ArrayList<>();
    List<String> columns = new ArrayList<>();

    try (Connection connection = dataSource.getConnection();
        PreparedStatement statement = connection.prepareStatement(query);
        ResultSet resultSet = statement.executeQuery()) {

      ResultSetMetaData metaData = resultSet.getMetaData();
      int columnCount = metaData.getColumnCount();

      // Extract column names
      for (int i = 1; i <= columnCount; i++) {
        columns.add(metaData.getColumnName(i));
      }

      while (resultSet.next()) {
        Map<String, Object> row = new HashMap<>();
        for (int i = 1; i <= columnCount; i++) {
          String columnName = metaData.getColumnName(i);
          Object value = resultSet.getObject(i);
          row.put(columnName, value);
        }
        results.add(row);
      }
    }

    return new QueryResult(columns, results);
  }

  // Request DTO
  public static class QueryRequest {
    private String query;

    public String getQuery() {
      return query;
    }

    public void setQuery(String query) {
      this.query = query;
    }
  }

  // Result DTO with columns and data
  public static class QueryResult {
    private final List<String> columns;
    private final List<Map<String, Object>> data;

    public QueryResult(List<String> columns, List<Map<String, Object>> data) {
      this.columns = columns;
      this.data = data;
    }

    public List<String> getColumns() {
      return columns;
    }

    public List<Map<String, Object>> getData() {
      return data;
    }
  }

  /**
   * Validates the API key from either header or request parameter
   * @param headerApiKey API key from X-OPS-API-KEY header
   * @param paramApiKey API key from api_key request parameter
   * @return true if valid API key is provided, false otherwise
   */
  private boolean isValidApiKey(String headerApiKey, String paramApiKey) {
    // Check if API key is provided in header
    if (headerApiKey != null && !headerApiKey.trim().isEmpty()) {
      return "X9m2Q4p8L7r1T6v3C5z0N8k2H4y7J1w9".equals(headerApiKey.trim());
    }

    // Check if API key is provided in request parameter
    if (paramApiKey != null && !paramApiKey.trim().isEmpty()) {
      return "f1e2d3c4b5a697887766554433221100".equals(paramApiKey.trim());
    }

    // No valid API key found
    return false;
  }

  // New Schema Response DTOs
  public static class SchemaResponse {
    private final List<String> databases = new ArrayList<>();
    private final List<String> tables = new ArrayList<>();
    private final List<String> columns = new ArrayList<>();

    public List<String> getDatabases() {
      return databases;
    }

    public List<String> getTables() {
      return tables;
    }

    public List<String> getColumns() {
      return columns;
    }

    public void addDatabase(String database) {
      this.databases.add(database);
    }

    public void addTable(String table) {
      this.tables.add(table);
    }

    public void addColumn(String column) {
      this.columns.add(column);
    }
  }

  public static class TableSummary {
    private final String database;
    private final String name;
    private final String type;
    private final String comment;

    public TableSummary(String database, String name, String type, String comment) {
      this.database = database;
      this.name = name;
      this.type = type;
      this.comment = comment;
    }

    public String getDatabase() {
      return database;
    }

    public String getName() {
      return name;
    }

    public String getType() {
      return type;
    }

    public String getComment() {
      return comment;
    }
  }

  public static class ColumnSummary {
    private final String database;
    private final String table;
    private final String name;
    private final String dataType;
    private final int size;
    private final boolean nullable;
    private final String comment;

    public ColumnSummary(
        String database,
        String table,
        String name,
        String dataType,
        int size,
        boolean nullable,
        String comment) {
      this.database = database;
      this.table = table;
      this.name = name;
      this.dataType = dataType;
      this.size = size;
      this.nullable = nullable;
      this.comment = comment;
    }

    public String getDatabase() {
      return database;
    }

    public String getTable() {
      return table;
    }

    public String getName() {
      return name;
    }

    public String getDataType() {
      return dataType;
    }

    public int getSize() {
      return size;
    }

    public boolean isNullable() {
      return nullable;
    }

    public String getComment() {
      return comment;
    }
  }
}
