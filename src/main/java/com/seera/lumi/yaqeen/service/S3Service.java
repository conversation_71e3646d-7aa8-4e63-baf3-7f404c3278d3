package com.seera.lumi.yaqeen.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class S3Service {

  private final S3Client s3Client;

  @Value("${aws.s3.bucket-name:}")
  private String bucketName;

  @Value("${aws.s3.base-url:}")
  private String baseUrl;

  /**
   * Upload multiple files to S3 and return their URLs
   * @param files Map of filename to File objects
   * @return List of S3 URLs
   */
  public List<String> uploadFiles(Map<String, File> files) {
    List<String> urls = new ArrayList<>();
    
    if (s3Client == null) {
      log.warn("S3 client is not configured. Cannot upload files.");
      return urls;
    }

    if (bucketName.isEmpty()) {
      log.warn("S3 bucket name is not configured. Cannot upload files.");
      return urls;
    }

    for (Map.Entry<String, File> entry : files.entrySet()) {
      try {
        String url = uploadFile(entry.getKey(), entry.getValue());
        if (url != null) {
          urls.add(url);
        }
      } catch (Exception e) {
        log.error("Failed to upload file {}: {}", entry.getKey(), e.getMessage(), e);
      }
    }

    return urls;
  }

  /**
   * Upload a single file to S3
   * @param fileName Name of the file
   * @param file File object to upload
   * @return S3 URL of uploaded file or null if failed
   */
  public String uploadFile(String fileName, File file) {
    if (s3Client == null) {
      log.warn("S3 client is not configured. Cannot upload file: {}", fileName);
      return null;
    }

    if (bucketName.isEmpty()) {
      log.warn("S3 bucket name is not configured. Cannot upload file: {}", fileName);
      return null;
    }

    try {
      // Generate unique key with timestamp
      String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
      String key = String.format("attachments/%s_%s", timestamp, fileName);

      // Determine content type
      String contentType = getContentType(fileName);

      // Create put request
      PutObjectRequest putObjectRequest = PutObjectRequest.builder()
          .bucket(bucketName)
          .key(key)
          .contentType(contentType)
          .build();

      // Upload file
      s3Client.putObject(putObjectRequest, RequestBody.fromFile(file));

      // Generate URL
      String url = generateUrl(key);
      log.info("Successfully uploaded file {} to S3: {}", fileName, url);
      
      return url;

    } catch (S3Exception e) {
      log.error("S3 error uploading file {}: {} (Error Code: {})", fileName, e.getMessage(), e.awsErrorDetails().errorCode());
      return null;
    } catch (Exception e) {
      log.error("Error uploading file {} to S3: {}", fileName, e.getMessage(), e);
      return null;
    }
  }

  /**
   * Generate S3 URL for a given key
   */
  private String generateUrl(String key) {
    if (!baseUrl.isEmpty()) {
      return String.format("%s/%s/%s", baseUrl, bucketName, key);
    }
    // Default AWS S3 URL format
    return String.format("https://%s.s3.amazonaws.com/%s", bucketName, key);
  }

  /**
   * Determine content type based on file extension
   */
  private String getContentType(String fileName) {
    String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    
    return switch (extension) {
      case "csv" -> "text/csv";
      case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      case "xls" -> "application/vnd.ms-excel";
      case "pdf" -> "application/pdf";
      case "txt" -> "text/plain";
      case "json" -> "application/json";
      case "xml" -> "application/xml";
      default -> "application/octet-stream";
    };
  }

  /**
   * Check if S3 service is properly configured
   */
  public boolean isConfigured() {
    return s3Client != null && !bucketName.isEmpty();
  }
}
