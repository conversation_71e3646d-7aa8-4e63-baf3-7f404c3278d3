info.app:
  description: Application for yaqeen core feature.
  version: ${BUILD_REVISION:1.0.0}

server:
  forward-headers-strategy: framework
  port: ${SERVICE_PORT:${SERVER_PORT:8182}}
  compression:
    enabled: true
    mime-types: text/html, text/xml, text/plain, text/css, text/javascript, application/javascript, application/json

api:
  carpro:
    base.url: ${CAR_PRO_SERVICE:https://api-dev.lumirental.com/core-carpro-service}
  petromin:
    base.url: ${PETROMIN_SERVICE:https://mobility.ntsc.app/api/}
    username: ${PETROMIN_USERNAME:<EMAIL>}
    password: ${PETROMIN_PASSWORD:gautam@123}
  saferoad:
    base.url: ${SAFEROAD_SERVICE:https://fms-api-hcr64pytia-ez.a.run.app/}
    username: ${SAFEROAD_USERNAME:lumi.api}
    password: ${SAFEROAD_PASSWORD:}
  fleet:
    base.url: ${FLEET_SERVICE_BASE_URL:https://api.lumirental.com/core-fleet-service}

kafka:
  dlt.listen.auto.start: ${DLT_AUTO_START:true}
  listen:
    auto.start: true
    concurrency: ${KAFKA_LISTENER_CONCURRENCY:1}
  topic:
    customer.data: ${KAFKA_TOPIC_CUSTOMER_DATA:SyncCustomersKafkaTopic}
    replication.task: replicationTaskQueue
    vehicle.financial.data: SyncVehicleFinancialData
    audit.log: ${KAFKA_TOPIC_AUDIT_LOG:audit_event}
    business.audit.events: ${KAFKA_TOPIC_BUSINESS_AUDIT_EVENTS:business_audit_events}

logging:
  level:
    org: WARN
    root: WARN
    com:
      seera: INFO


management:
  endpoints.web.exposure.include: health,info,prometheus
  health:
    mail:
      enabled: false

security.basic.enable: false


spring:
  application.name: lease-core-yaqeen-business
  profiles.active: ${ACTIVE_PROFILE:local}
  threads.virtual.enabled: 'true'
  cloud:
    openfeign:
      client.config.default:
        connect-timeout: 500000
        micrometer.enabled: true
        read-timeout: 500000
      compression.response.enabled: true
      http2client.enabled: true
      httpclient:
        connection-timeout: 500000
        connection-timer-repeat: 300000
        hc5.enabled: true
        max-connections: 250
        ok-http.read-timeout: 500s
      okhttp.enabled: true
    stream.kafka.binder:
      minPartitionCount: ${KAFKA_PARTITION:1}
      replicationFactor: ${KAFKA_REPLICATION:1}
  data:
    redis:
      host: ${SPRING_REDIS_HOST:localhost}
      password: ${SPRING_REDIS_PASSWORD:}
      port: ${SPRING_REDIS_PORT:6379}
      ssl.enabled: ${SPRING_REDIS_SSL:false}
      timeout: 60
  datasource:
    hikari:
      auto-commit: true
      connection-timeout: ${DB_TIMEOUT:300000}
      idle-timeout: ${DB_IDLE_TIME:300000}
      maximum-pool-size: ${DB_POOL:50}
      minimum-idle: ${DB_MIN_IDLE:10}
    testWhileIdle: true
    validationQuery: SELECT 1
  jpa:
    generate-ddl: false
    show-sql: false
    open-in-view: false
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: none
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
  kafka:
    bootstrap-servers: ${KAFKA_HOST:localhost:9092}
    default.bootstrap-servers: ${KAFKA_DEFAULT_HOST:localhost:9092}
    consumer:
      auto-offset-reset: earliest
      group-id: ${spring.application.name}
  liquibase:
    change-log: classpath:/db/migration/changelog-master.xml
    contexts: dev,qa,uat,prod
    enabled: false
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: yayxdbyqqtbjlfdo
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.gmail.com


springdoc.swagger-ui:
  host: ${SWAGGER_HOST:}
  path: /swagger-ui.html

swagger-context: ${SWAGGER_CONTEXT:}

# Audit Configuration
audit:
  skip-tables: audit_event,audit_log,replication_job_history,replication_task_history
