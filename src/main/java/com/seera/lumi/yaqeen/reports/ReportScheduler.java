package com.seera.lumi.yaqeen.reports;

import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.google.api.services.drive.Drive;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.seera.lumi.yaqeen.service.EmailService;
import com.seera.lumi.yaqeen.dto.CustomReportRequest;

@Slf4j
@Component
@RequiredArgsConstructor
public class ReportScheduler {

  private final JdbcTemplate jdbcTemplate;
  private final Drive drive;
  private final EmailService emailService;
  private String googleDriveFolderId = "1lQqbbOXT88db7w3pTONp3R0Vu7yjFMEo";
  private static final ZoneId SAUDI_ZONE = ZoneId.of("Asia/Riyadh");
  private static final DateTimeFormatter DATE_FORMATTER =
      DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(SAUDI_ZONE);
  private static final DateTimeFormatter FILE_DATE_FORMATTER =
      DateTimeFormatter.ofPattern("yyyyMMdd").withZone(SAUDI_ZONE);
  private static final DateTimeFormatter FOLDER_DATE_FORMATTER =
      DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(SAUDI_ZONE);

  public void generateAllReports() {

    Map<String, java.io.File> reports = new HashMap<>();
    // generate all reports and send email to lumi finance
    try {
      reports.put("Open Agreement Report.csv", openAgreementReport());
    } catch (Exception e) {
      log.error("Error in OpenAgreementReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate open agreement report", e);
    }

    try {
      reports.put("Agreement Report.csv", agreementReport());
    } catch (Exception e) {
      log.error("Error in AgreementReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate agreement report", e);
    }

    try {
      reports.put("Invoice Report.csv", invoiceReport());
    } catch (Exception e) {
      log.error("Error in InvoiceReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate invoice report", e);
    }

    try {
      reports.put("Agreement Status Report.csv", agreementStatusReport());
    } catch (Exception e) {
      log.error("Error in AgreementStatusReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate agreement status report", e);
    }

    try {
      reports.put("Daily Cash Report.xlsx", dailyCashReportDriver());
    } catch (Exception e) {
      log.error("Error in DailyCashReportDriver job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate daily cash report driver", e);
    }

    try {
      reports.put("Monthly Cash Report.csv", monthlyCashReport());
    } catch (Exception e) {
      log.error("Error in MonthlyCashReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate monthly cash report", e);
    }

    try {
      reports.put("Vehicle Fleet Status Report.csv", vehicleFleetStatusReport());
    } catch (Exception e) {
      log.error("Error in VehicleFleetStatusReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate vehicle fleet status report", e);
    }

    emailService.sendFinanceReportsEmail(reports);
    log.info("All reports generated and sent successfully");

    reports
        .values()
        .forEach(
            file -> {
              try {
                file.delete();
              } catch (Exception e) {
                log.error("Error deleting file {}: {}", file.getName(), e.getMessage());
              }
            });
  }

  public void generateTrafficFineReports() {
    Map<String, java.io.File> reports = new HashMap<>();
    // generate all reports and send email to lumi finance
    try {
      reports.put("Traffic Fine Report.csv", trafficFineReport());
    } catch (Exception e) {
      log.error("Error in TrafficFineReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate traffic fine report", e);
    }

    emailService.sendTrafficFineEmail(reports);
    log.info("Traffic fine reports generated and sent successfully");

    reports
        .values()
        .forEach(
            file -> {
              try {
                file.delete();
              } catch (Exception e) {
                log.error("Error deleting file {}: {}", file.getName(), e.getMessage());
              }
            });
  }

  public void generateAndSendCustomReports(CustomReportRequest request) {
    Map<String, java.io.File> attachments = new HashMap<>();
    try {
      if (request.getReports() != null) {
        for (Map.Entry<String, String> entry : request.getReports().entrySet()) {
          String filename = entry.getKey();
          String query = entry.getValue();
          log.info("Generating report for filename: {}", filename);
          log.info("Query: {}", query);
          if ("DAILY_CASH_EXCEL".equalsIgnoreCase(query)) {
            log.info("Generating daily cash report workbook");
            // Special token to include the multi-sheet daily cash report workbook
            java.io.File excel = dailyCashReportDriver();
            attachments.put(filename.endsWith(".xlsx") ? filename : filename + ".xlsx", excel);
          } else {
            java.io.File csv = generateCsvFromQuery(filename, query);
            log.info("Generated CSV report for filename: {}", filename);
            attachments.put(filename.endsWith(".csv") ? filename : filename + ".csv", csv);
          }
        }
      }
      // upload to s3
      emailService.sendCsvFileEmail(
          request.getTo(), request.getCc(), request.getBcc(), request.getSubject(), request.getBody(), attachments);
    } finally {
      // cleanup temp files
      for (java.io.File file : attachments.values()) {
        try { file.delete(); } catch (Exception ignore) {}
      }
    }
  }

  private java.io.File generateCsvFromQuery(String filename, String query) {
    try {
      List<Map<String, Object>> rows = jdbcTemplate.queryForList(query);
      java.io.File csvFile = new java.io.File(filename.endsWith(".csv") ? filename : filename + ".csv");
      if (rows.isEmpty()) {
        try (FileWriter writer = new FileWriter(csvFile)) {
          writer.write("");
        }
        return csvFile;
      }

      try (FileWriter writer = new FileWriter(csvFile)) {
        // header from first row keys
        String header = String.join(",", rows.get(0).keySet());
        writer.write(header + "\n");
        for (Map<String, Object> row : rows) {
          StringBuilder line = new StringBuilder();
          boolean first = true;
          for (String key : rows.get(0).keySet()) {
            if (!first) line.append(',');
            Object val = row.get(key);
            line.append(val == null ? "" : escapeCsv(val.toString()));
            first = false;
          }
          writer.write(line.toString());
          writer.write("\n");
        }
      }
      
      return csvFile;
    } catch (IOException e) {
      throw new RuntimeException("Failed to write CSV for " + filename, e);
    }
  }

  private String escapeCsv(String value) {
    if (value.contains(",") || value.contains("\n") || value.contains("\"")) {
      return '"' + value.replace("\"", "\"\"") + '"';
    }
    return value;
  }

  private java.io.File openAgreementReport() {
    String jobName = "OpenAgreementReport";
    String CSV_HEADER =
        "agreement_no,driver_name,cdp,checkout_branch,checkin_branch,checkout_date,checkin_date,status,charge_group,car_group,plate_no,plate_no_ar,make,model,phone_number\n";
    log.info(" ***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query... for open agreement report");
      String query =
          "SELECT a.agreement_no, "
              + "CONCAT(d.first_name,' ', d.last_name) as driver_name, "
              + "a.promotion_code as cdp, "
              + "b1.code as checkout_branch, "
              + "b2.code as checkin_branch, "
              + "a.checkout_date as checkout_date, "
              + "a.checkin_date as checkin_date, "
              + "CASE a.status "
              + "WHEN 0 THEN 'Pending' "
              + "WHEN 1 THEN 'Open' "
              + "WHEN 2 THEN 'Closed' "
              + "WHEN 3 THEN 'Cancelled' "
              + "WHEN 4 THEN 'Closure Pending' "
              + "WHEN 5 THEN 'Suspended' "
              + "ELSE 'unknown' "
              + "END AS status, "
              + "vg1.code as charge_group, "
              + "vg2.code as car_group, "
              + "av.plate_no, "
              + "v.plate_no_ar, "
              + "ma.name_en as make, "
              + "m.name_en as model, "
              + "d.mobile_number as phone_number "
              + "FROM `lumi-core-agreement-service`.agreement a "
              + "left join `lumi-core-agreement-service`.agreement_financials af on af.agreement_no = a.agreement_no "
              + "left join `lumi-core-agreement-service`.driver d on a.driver_id = d.id "
              + "left join `branch_service`.branch b1 on a.checkout_branch = b1.id "
              + "left join `branch_service`.branch b2 on a.checkin_branch = b2.id "
              + "left join `lumi-core-agreement-service`.agreement_vehicle av on a.agreement_vehicle_id = av.id "
              + "left join `lumi-core-fleet-service`.vehicle v on av.plate_no = v.plate_no "
              + "left join `lumi-core-fleet-service`.model m on v.model_id = m.id "
              + "left join `lumi-core-fleet-service`.make ma on m.make_id = ma.id "
              + "left join `lumi-core-fleet-service`.vehicle_group vg1 on vg1.id = af.charge_vehicle_group_id "
              + "left join `lumi-core-fleet-service`.vehicle_group vg2 on vg2.id = av.vehicle_group_id "
              + "where a.status = 1";

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info(
          "Query executed successfully for open agreement report. Found {} records",
          results.size());

      // Create CSV file
      String timestamp = ZonedDateTime.now(SAUDI_ZONE).format(FILE_DATE_FORMATTER);
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_open_agreements.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                  row.get("agreement_no"),
                  row.get("driver_name") == null ? "" : row.get("driver_name"),
                  row.get("cdp") == null ? "" : row.get("cdp"),
                  row.get("checkout_branch"),
                  row.get("checkin_branch"),
                  formatDate(row.get("checkout_date")),
                  formatDate(row.get("checkin_date")),
                  row.get("status"),
                  row.get("charge_group"),
                  row.get("car_group"),
                  row.get("plate_no"),
                  row.get("plate_no_ar"),
                  row.get("make"),
                  row.get("model"),
                  row.get("phone_number")));
        }
      }
      log.info("CSV file created successfully for open agreement report");

      return csvFile;

    } catch (Exception e) {
      log.error("Error in OpenAgreementReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate and upload report", e);
    }
  }

  private java.io.File agreementReport() {
    String jobName = "AgreementReport";
    String CSV_HEADER =
        "agreement_no,driver_name,cdp/discount_code,checkout_branch,checkin_branch,checkout_date,checkin_date,status,charge_group,car_group,plate_no,plate_no_ar,make,model,phone_number\n";
    log.info(" ***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query... for agreement report");
      String query =
          "SELECT a.agreement_no, "
              + "CONCAT(d.first_name,' ', d.last_name) as driver_name, "
              + "a.promotion_code as cdp, "
              + "b1.code as checkout_branch, "
              + "b2.code as checkin_branch, "
              + "a.checkout_date as checkout_date, "
              + "a.checkin_date as checkin_date, "
              + "CASE a.status "
              + "WHEN 0 THEN 'Pending' "
              + "WHEN 1 THEN 'Open' "
              + "WHEN 2 THEN 'Closed' "
              + "WHEN 3 THEN 'Cancelled' "
              + "WHEN 4 THEN 'Closure Pending' "
              + "WHEN 5 THEN 'Suspended' "
              + "ELSE 'unknown' "
              + "END AS status, "
              + "vg1.code as charge_group, "
              + "vg2.code as car_group, "
              + "av.plate_no, "
              + "v.plate_no_ar, "
              + "ma.name_en as make, "
              + "m.name_en as model, "
              + "d.mobile_number as phone_number "
              + "FROM `lumi-core-agreement-service`.agreement a "
              + "left join `lumi-core-agreement-service`.agreement_financials af on af.agreement_no = a.agreement_no "
              + "left join `lumi-core-agreement-service`.driver d on a.driver_id = d.id "
              + "left join `branch_service`.branch b1 on a.checkout_branch = b1.id "
              + "left join `branch_service`.branch b2 on a.checkin_branch = b2.id "
              + "left join `lumi-core-agreement-service`.agreement_vehicle av on a.agreement_vehicle_id = av.id "
              + "left join `lumi-core-fleet-service`.vehicle v on av.plate_no = v.plate_no "
              + "left join `lumi-core-fleet-service`.model m on v.model_id = m.id "
              + "left join `lumi-core-fleet-service`.make ma on m.make_id = ma.id "
              + "left join `lumi-core-fleet-service`.vehicle_group vg1 on vg1.id = af.charge_vehicle_group_id "
              + "left join `lumi-core-fleet-service`.vehicle_group vg2 on vg2.id = av.vehicle_group_id "
              + "where DATE(a.created_on) >= '2025-05-01'";

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info(
          "Query executed successfully for agreement report. Found {} records", results.size());

      // Create CSV file
      String timestamp = ZonedDateTime.now(SAUDI_ZONE).format(FILE_DATE_FORMATTER);
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_agreements.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                  row.get("agreement_no"),
                  row.get("driver_name") == null ? "" : row.get("driver_name"),
                  row.get("cdp") == null ? "" : row.get("cdp"),
                  row.get("checkout_branch"),
                  row.get("checkin_branch"),
                  formatDate(row.get("checkout_date")),
                  formatDate(row.get("checkin_date")),
                  row.get("status").equals("Closure Pending") ? "Open" : row.get("status"),
                  row.get("charge_group"),
                  row.get("car_group"),
                  row.get("plate_no"),
                  row.get("plate_no_ar"),
                  row.get("make"),
                  row.get("model"),
                  row.get("phone_number")));
        }
      }
      log.info("CSV file created successfully for agreement report");

      return csvFile;
    } catch (Exception e) {
      log.error("Error in AgreementReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate and upload report", e);
    }
  }

  private java.io.File invoiceReport() {
    String jobName = "InvoiceReport";
    String CSV_HEADER =
        "invoice_number,invoice_type,invoice_date,agreement_number,checkout_date,driver_name,debtor_code,currency,total_amount,balance\n";
    log.info(" ***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query... for invoice report");
      String query =
          """
          SELECT
              i.invoice_number,
              CONCAT(i.invoice_type, '-', i.invoice_category) AS invoice_type,
              i.issue_date AS invoice_date,
              i.agreement_number,
              a.checkout_date,
              CONCAT(d.first_name, ' ', d.last_name) AS driver_name,
              a.debtor_code,
              'SAR' AS currency,
              i.total_invoice_after_vat AS total_amount,
              (i.total_invoice_after_vat - i.total_amount_paid) AS balance
          FROM
              `lumi-core-agreement-service`.invoice i
          LEFT JOIN
              `lumi-core-agreement-service`.agreement a
              ON i.agreement_number = a.agreement_no
          LEFT JOIN
              `lumi-core-agreement-service`.driver d
              ON a.driver_id = d.id
          WHERE
              DATE(i.issue_date) >= '2025-05-01'
          ORDER BY
              i.issue_date DESC;
          """;
      ;

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info("Query executed successfully for invoice report. Found {} records", results.size());

      // Create CSV file
      String timestamp = ZonedDateTime.now(SAUDI_ZONE).format(FILE_DATE_FORMATTER);
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_invoices.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                  row.get("invoice_number"),
                  row.get("invoice_type"),
                  formatDate(row.get("invoice_date")),
                  row.get("agreement_number"),
                  formatDate(row.get("checkout_date")),
                  row.get("driver_name"),
                  row.get("debtor_code") == null || row.get("debtor_code").equals("DEFAULT")
                      ? ""
                      : row.get("debtor_code"),
                  row.get("currency"),
                  row.get("total_amount"),
                  row.get("balance")));
        }
      }
      log.info("CSV file created successfully for invoice report");

      return csvFile;
    } catch (Exception e) {
      log.error("Error in InvoiceReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate and upload report", e);
    }
  }

  private java.io.File agreementStatusReport() {
    String jobName = "AgreementStatusReport";
    String CSV_HEADER =
        "AGREEMENT_STATUS,LICENCE_NO,CAR_STATUS,CM_NAME,CD_NAME,CHECK_OUT_BRANCH,BRANCH_NAME,CHECK_OUT_DATE,CHECK_IN_DATE,TOTAL_DAYS,SOLD_DAYS_IN_SECONDS,F_GROUP,AGREEMENT_NO,DEBITOR_NAME,RATE_NO,RATE_DATE,PACKAGE_NAME,RENTAL_PRICE_BEFORE_DISCOUNT,RENTAL_DISCOUNT_PRICE,KM_SUM,EXTRA_FUEL_SUM,INSURANCE_SUM,OTHER_SUM,UNIT_NO,CDP_COMPANY_NAME,TOTALPAIDAMOUNT,DEBITOR_CODE,DRIVER_CODE,FIRST_NAME,LAST_NAME\n";
    log.info("***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query...");
      String query =
          """
          SELECT CASE a.status
                  WHEN 0 THEN 'Pending'
                  WHEN 1 THEN 'Open'
                  WHEN 2 THEN 'Closed'
                  WHEN 3 THEN 'Cancelled'
                  WHEN 4 THEN 'Closure Pending'
                  WHEN 5 THEN 'Suspended'
                  ELSE 'Unknown Status'
              end                                           AS AGREEMENT_STATUS,
              av.plate_no                                   LICENCE_NO,
              Json_extract(vo.vehicle_status, '$.statusId') AS CAR_STATUS,
              vamke.name_en                                 CM_NAME,
              vm.name_en                                    CD_NAME,
              a.checkout_branch                             CHECK_OUT_BRANCH,
              mb.en                                         BRANCH_NAME,
              a.checkout_date                               CHECK_OUT_DATE,
              Datediff(a.checkout_date, a.checkin_date)     TOTAL_DAYS,
              af.sold_days_in_seconds                       SOLD_DAYS_IN_SECONDS,
              vg.code                                       F_GROUP,
              a.agreement_no                                AGREEMENT_NO,
              a.checkin_date                                CHECK_IN_DATE,
              Json_value(a.metadata, '$.debtorName')        DEBITOR_NAME,
              bp.rate_code                                  RATE_NO,
              ''                                            RATE_DATE,
              ''                                            PACKAGE_NAME,
              af.rental_sum
              RENTAL_PRICE_BEFORE_DISCOUNT,
              af.discounted_rental_sum                      RENTAL_DISCOUNT_PRICE,
              af.extra_km_charge_sum                        KM_SUM,
              af.extra_fuel_charge_sum                      EXTRA_FUEL_SUM,
              af.insurance_sum                              INSURANCE_SUM,
              af.addons_sum                                 OTHER_SUM,
              v.asset_id                                    UNIT_NO,
              a.promotion_code                              CDP_COMPANY_NAME,
              (SELECT Sum(amount)
                  FROM   payment_service.payment
                  WHERE  track_id = a.quotation_reference
                      AND payment_status = 'SUCCESS')       TOTALPAIDAMOUNT,
              a.debtor_code                                 DEBITOR_CODE,
              d.driver_code                                 DRIVER_CODE,
              d.first_name                                  FIRST_NAME,
              d.last_name                                   LAST_NAME
          FROM   `lumi-core-agreement-service`.agreement a
              INNER JOIN `booking_service`.booking b
                      ON a.booking_id = b.id
              INNER JOIN `branch_service`.branch br
                      ON br.id = a.checkout_branch
              INNER JOIN `branch_service`.multilingual mb
                      ON mb.id = br.name_id
              INNER JOIN `booking_service`.booking_price bp
                      ON bp.id = b.booking_price_id
              INNER JOIN `lumi-core-agreement-service`.agreement_vehicle av
                      ON a.agreement_vehicle_id = av.id
              INNER JOIN `lumi-core-agreement-service`.agreement_financials af
                      ON a.agreement_no = af.agreement_no
              INNER JOIN `lumi-core-agreement-service`.driver d
                      ON a.driver_id = d.id
              INNER JOIN `lumi-core-fleet-service`.vehicle_operational_data vo
                      ON av.plate_no = vo.plate_no
              INNER JOIN `lumi-core-fleet-service`.vehicle v
                      ON av.plate_no = v.plate_no
              INNER JOIN `lumi-core-fleet-service`.model vm
                      ON v.model_id = vm.id
              INNER JOIN `lumi-core-fleet-service`.make vamke
                      ON vamke.id = vm.make_id
              INNER JOIN `lumi-core-fleet-service`.vehicle_group vg
                      ON vm.group_code = vg.code
                  WHERE DATE(a.created_on) >= '2025-07-25';
              """;

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info("Query executed successfully. Found {} records", results.size());

      // Create CSV file
      String timestamp = ZonedDateTime.now(SAUDI_ZONE).format(FILE_DATE_FORMATTER);
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_agreement_status_report.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                  row.get("AGREEMENT_STATUS"),
                  row.get("LICENCE_NO") == null ? "" : row.get("LICENCE_NO"),
                  row.get("CAR_STATUS") == null ? "" : row.get("CAR_STATUS"),
                  row.get("CM_NAME") == null ? "" : row.get("CM_NAME"),
                  row.get("CD_NAME") == null ? "" : row.get("CD_NAME"),
                  row.get("CHECK_OUT_BRANCH") == null ? "" : row.get("CHECK_OUT_BRANCH"),
                  row.get("BRANCH_NAME") == null ? "" : row.get("BRANCH_NAME"),
                  formatDate(row.get("CHECK_OUT_DATE")),
                  formatDate(row.get("CHECK_IN_DATE")),
                  row.get("TOTAL_DAYS") == null ? "" : row.get("TOTAL_DAYS"),
                  row.get("SOLD_DAYS_IN_SECONDS") == null ? "" : row.get("SOLD_DAYS_IN_SECONDS"),
                  row.get("F_GROUP") == null ? "" : row.get("F_GROUP"),
                  row.get("AGREEMENT_NO") == null ? "" : row.get("AGREEMENT_NO"),
                  row.get("DEBITOR_NAME") == null ? "" : row.get("DEBITOR_NAME"),
                  row.get("RATE_NO") == null ? "" : row.get("RATE_NO"),
                  row.get("RATE_DATE") == null ? "" : row.get("RATE_DATE"),
                  row.get("PACKAGE_NAME") == null ? "" : row.get("PACKAGE_NAME"),
                  row.get("RENTAL_PRICE_BEFORE_DISCOUNT") == null
                      ? ""
                      : row.get("RENTAL_PRICE_BEFORE_DISCOUNT"),
                  row.get("RENTAL_DISCOUNT_PRICE") == null ? "" : row.get("RENTAL_DISCOUNT_PRICE"),
                  row.get("KM_SUM") == null ? "" : row.get("KM_SUM"),
                  row.get("EXTRA_FUEL_SUM") == null ? "" : row.get("EXTRA_FUEL_SUM"),
                  row.get("INSURANCE_SUM") == null ? "" : row.get("INSURANCE_SUM"),
                  row.get("OTHER_SUM") == null ? "" : row.get("OTHER_SUM"),
                  row.get("UNIT_NO") == null ? "" : row.get("UNIT_NO"),
                  row.get("CDP_COMPANY_NAME") == null ? "" : row.get("CDP_COMPANY_NAME"),
                  row.get("TOTALPAIDAMOUNT") == null ? "" : row.get("TOTALPAIDAMOUNT"),
                  row.get("DEBITOR_CODE") == null ? "" : row.get("DEBITOR_CODE"),
                  row.get("DRIVER_CODE") == null ? "" : row.get("DRIVER_CODE"),
                  row.get("FIRST_NAME") == null ? "" : row.get("FIRST_NAME"),
                  row.get("LAST_NAME") == null ? "" : row.get("LAST_NAME")));
        }
      }
      log.info("CSV file created successfully");
      return csvFile;
    } catch (Exception e) {
      log.error("Error in AgreementStatusReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate agreement status report", e);
    }
  }

  private java.io.File dailyCashReportDriver() {
    String jobName = "DailyCashReportDriver";
    log.info("***** Started Job {} *****", jobName);

    try {
      // Get all active branches that are yaqeen migrated
      log.info("Fetching active branches...");
      String branchQuery =
          "SELECT b.id, b.code FROM branch_service.branch b WHERE b.is_yaqeen_migrated = true";
      List<Map<String, Object>> branches = jdbcTemplate.queryForList(branchQuery);
      log.info("Found {} active branches", branches.size());

      // Create Excel workbook
      String excelFileName = "daily_cash_reports_all_branches.xlsx";
      java.io.File excelFile = new java.io.File(excelFileName);

      log.info("Creating Excel file: {}", excelFileName);

      try (Workbook workbook = new XSSFWorkbook();
          FileOutputStream fileOut = new FileOutputStream(excelFile)) {

        // Create header style
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        int successCount = 0;
        int totalRecords = 0;

        // Process each branch and create separate sheet
        for (Map<String, Object> branch : branches) {
          String branchId = branch.get("id").toString();
          log.info("Processing branch ID: {}", branchId);

          try {
            // Get daily cash report data for this branch using the same query logic
            String query =
                """
                    SELECT      p.id AS receipt_id,
                                a.agreement_no,
                                a.booking_no,
                                i.invoice_number,
                                p.amount,
                                p.paid_through,
                                u.success_factor_id,
                                r.id AS register_id
                        FROM      `payment_service`.payment p
                        LEFT JOIN `payment_service`.payment_detail pd
                        ON        p.payment_id = pd.payment_id
                        LEFT JOIN `lumi-core-agreement-service`.agreement a
                        ON        p.track_id = a.quotation_reference
                        LEFT JOIN `lumi-core-agreement-service`.invoice i
                        ON        i.agreement_number = a.agreement_no
                        LEFT JOIN `lumi-core-user-service`.user u
                        ON        u.external_id = p.processed_by
                        LEFT JOIN `payment_service`.register r
                        ON        r.id = pd.register_id
                        WHERE     r.branch_id = ?
                        AND       p.payment_status = 'SUCCESS'
                        AND       p.paid_through in ('CASH', 'BANK_TRANSFER', 'POS')
                        AND       date(p.created_on) = curdate() - interval 1 day
                        UNION ALL
                        SELECT    r.id AS receipt_id,
                                a.agreement_no,
                                a.booking_no,
                                i.invoice_number,
                                -r.amount,
                                r.refund_through,
                                u.success_factor_id,
                                re.id AS register_id
                        FROM      payment_service.refund r
                        LEFT JOIN `lumi-core-agreement-service`.agreement a
                        ON        r.track_id = a.quotation_reference
                        LEFT JOIN `lumi-core-agreement-service`.invoice i
                        ON        i.agreement_number = a.agreement_no
                        LEFT JOIN `lumi-core-user-service`.user u
                        ON        u.external_id = r.processed_by
                        LEFT JOIN `payment_service`.register re
                        ON        re.id = r.register_id
                        WHERE     r.refund_through IN ('BANK_TRANSFER', 'CASH', 'POS')
                        AND       r.status = 'SUCCESS'
                        AND       re.branch_id = ?
                        AND       date(r.created_on) = curdate() - interval 1 day
                """;

            List<Map<String, Object>> results =
                jdbcTemplate.queryForList(query, branchId, branchId);
            log.info("Found {} records for branch {}", results.size(), branchId);

            // Create sheet for this branch
            Sheet sheet = workbook.createSheet(branch.get("code").toString());

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
              "Receipt ID",
              "Agreement No",
              "Booking No",
              "Invoice Number",
              "Amount",
              "Payment Mode",
              "User ID",
              "Register ID"
            };

            for (int i = 0; i < headers.length; i++) {
              Cell cell = headerRow.createCell(i);
              cell.setCellValue(headers[i]);
              cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (Map<String, Object> row : results) {
              Row dataRow = sheet.createRow(rowNum++);

              // Receipt ID
              Cell cell0 = dataRow.createCell(0);
              cell0.setCellValue(
                  row.get("receipt_id") != null ? row.get("receipt_id").toString() : "");

              // Agreement No
              Cell cell1 = dataRow.createCell(1);
              cell1.setCellValue(
                  row.get("agreement_no") != null ? row.get("agreement_no").toString() : "");

              // Booking No
              Cell cell2 = dataRow.createCell(2);
              cell2.setCellValue(
                  row.get("booking_no") != null ? row.get("booking_no").toString() : "");

              // Invoice Number
              Cell cell3 = dataRow.createCell(3);
              cell3.setCellValue(
                  row.get("invoice_number") != null ? row.get("invoice_number").toString() : "");

              // Amount
              Cell cell4 = dataRow.createCell(4);
              if (row.get("amount") != null) {
                try {
                  cell4.setCellValue(Double.parseDouble(row.get("amount").toString()));
                } catch (NumberFormatException e) {
                  cell3.setCellValue(row.get("amount").toString());
                }
              }

              // Payment Mode
              Cell cell5 = dataRow.createCell(5);
              cell5.setCellValue(
                  row.get("paid_through") != null ? row.get("paid_through").toString() : "");

              // User ID
              Cell cell6 = dataRow.createCell(6);
              cell6.setCellValue(
                  row.get("success_factor_id") != null
                      ? row.get("success_factor_id").toString()
                      : "");

              // Register ID
              Cell cell7 = dataRow.createCell(7);
              cell7.setCellValue(
                  row.get("register_id") != null
                      ? getRegisterId(
                          branch.get("code").toString(), row.get("register_id").toString())
                      : "");

              totalRecords++;
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
              sheet.autoSizeColumn(i);
            }

            if (results.size() > 0) {
              successCount++;
            }

          } catch (Exception e) {
            log.error(
                "Error processing daily cash report for branch {}: {}", branchId, e.getMessage());
            // Create empty sheet for failed branch
            Sheet errorSheet = workbook.createSheet("Branch_" + branchId + "_ERROR");
            Row errorRow = errorSheet.createRow(0);
            Cell errorCell = errorRow.createCell(0);
            errorCell.setCellValue("Error: " + e.getMessage());
          }
        }

        // Create summary sheet
        Sheet summarySheet = workbook.createSheet("Summary");
        Row summaryHeaderRow = summarySheet.createRow(0);
        summaryHeaderRow.createCell(0).setCellValue("Daily Cash Reports Summary");
        summaryHeaderRow.getCell(0).setCellStyle(headerStyle);

        summarySheet.createRow(2).createCell(0).setCellValue("Total Branches:");
        summarySheet.getRow(2).createCell(1).setCellValue(branches.size());

        summarySheet.createRow(3).createCell(0).setCellValue("Successful Reports:");
        summarySheet.getRow(3).createCell(1).setCellValue(successCount);

        summarySheet.createRow(4).createCell(0).setCellValue("Failed Reports:");
        summarySheet.getRow(4).createCell(1).setCellValue(branches.size() - successCount);

        summarySheet.createRow(5).createCell(0).setCellValue("Total Records:");
        summarySheet.getRow(5).createCell(1).setCellValue(totalRecords);

        // Auto-size summary columns
        summarySheet.autoSizeColumn(0);
        summarySheet.autoSizeColumn(1);

        // Write workbook to file
        workbook.write(fileOut);
      }

      log.info("Excel file created successfully with {} branches", branches.size());
      return excelFile;

    } catch (Exception e) {
      log.error("Error in DailyCashReportDriver job: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to generate daily cash report driver", e);
    }
  }

  private java.io.File monthlyCashReport() {
    String jobName = "MonthlyCashReport";
    log.info("***** Started Job {} *****", jobName);

    try {
      // Fetch all yaqeen migrated branches
      log.info("Fetching yaqeen migrated branches for monthly cash report...");
      String branchQuery =
          "SELECT b.id, b.code FROM branch_service.branch b WHERE b.is_yaqeen_migrated = true";
      List<Map<String, Object>> branches = jdbcTemplate.queryForList(branchQuery);
      if (branches.isEmpty()) {
        log.warn("No yaqeen migrated branches found. Generating empty monthly report.");
      }

      // Build comma-separated list of branch IDs for IN clause
      StringBuilder branchIdsBuilder = new StringBuilder();
      for (int i = 0; i < branches.size(); i++) {
        if (i > 0) {
          branchIdsBuilder.append(",");
        }
        branchIdsBuilder.append(branches.get(i).get("id").toString());
      }
      String branchIdsCsv = branchIdsBuilder.toString();

      // Build query combining all branches with IN and current month date range (1st
      // to yesterday)
      String baseQuery =
          """
              SELECT      p.id AS receipt_id,
                          a.agreement_no,
                          a.booking_no,
                          i.invoice_number,
                          p.amount,
                          p.paid_through,
                          u.success_factor_id,
                          r.id AS register_id,
                          b.code AS branch_code,
                          DATE(p.created_on) AS transaction_date
                  FROM      `payment_service`.payment p
                  LEFT JOIN `payment_service`.payment_detail pd
                  ON        p.payment_id = pd.payment_id
                  LEFT JOIN `lumi-core-agreement-service`.agreement a
                  ON        p.track_id = a.quotation_reference
                  LEFT JOIN `lumi-core-agreement-service`.invoice i
                  ON        i.agreement_number = a.agreement_no
                  LEFT JOIN `lumi-core-user-service`.user u
                  ON        u.external_id = p.processed_by
                  LEFT JOIN `payment_service`.register r
                  ON        r.id = pd.register_id
                  LEFT JOIN `branch_service`.branch b
                  ON        b.id = r.branch_id
                  WHERE     p.payment_status = 'SUCCESS'
                  AND       date(p.created_on) BETWEEN DATE_FORMAT(CURDATE(), '%Y-%m-01') AND CURDATE() - INTERVAL 1 DAY
                  UNION ALL
                  SELECT    r.id AS receipt_id,
                            a.agreement_no,
                            a.booking_no,
                            i.invoice_number,
                            -r.amount,
                            r.refund_through,
                            u.success_factor_id,
                            re.id AS register_id,
                            b.code AS branch_code,
                            DATE(r.created_on) AS transaction_date
                  FROM      payment_service.refund r
                  LEFT JOIN `lumi-core-agreement-service`.agreement a
                  ON        r.track_id = a.quotation_reference
                  LEFT JOIN `lumi-core-agreement-service`.invoice i
                  ON        i.agreement_number = a.agreement_no
                  LEFT JOIN `lumi-core-user-service`.user u
                  ON        u.external_id = r.processed_by
                  LEFT JOIN `payment_service`.register re
                  ON        re.id = r.register_id
                  LEFT JOIN `branch_service`.branch b
                  ON        b.id = re.branch_id
                  WHERE     r.status = 'SUCCESS'
                  AND       date(r.created_on) BETWEEN DATE_FORMAT(CURDATE(), '%Y-%m-01') AND CURDATE() - INTERVAL 1 DAY
          """;

      log.info("Executing monthly cash report query for branches: {}", branchIdsCsv);
      List<Map<String, Object>> results = jdbcTemplate.queryForList(baseQuery);
      log.info("Monthly cash query executed. Found {} records", results.size());

      // Prepare CSV file
      String fileName =
          "monthly_cash_report_"
              + ZonedDateTime.now(SAUDI_ZONE).format(FILE_DATE_FORMATTER).substring(0, 6)
              + ".csv";
      java.io.File csvFile = new java.io.File(fileName);

      // Header similar to dailyCashReportDriver
      String[] headers = {
        "Receipt ID",
        "Agreement No",
        "Booking No",
        "Invoice Number",
        "Amount",
        "Payment Mode",
        "User ID",
        "Register ID",
        "Branch Code",
        "Transaction Date"
      };

      try (FileWriter writer = new FileWriter(csvFile)) {
        // Write header
        writer.write(String.join(",", headers));
        writer.write("\n");

        for (Map<String, Object> row : results) {
          String displayRegisterId = "";
          if (row.get("register_id") != null && row.get("branch_code") != null) {
            displayRegisterId =
                getRegisterId(row.get("branch_code").toString(), row.get("register_id").toString());
          }

          String amountStr = "";
          if (row.get("amount") != null) {
            try {
              amountStr = String.valueOf(Double.parseDouble(row.get("amount").toString()));
            } catch (NumberFormatException ex) {
              amountStr = row.get("amount").toString();
            }
          }

          writer.write(
              String.join(
                  ",",
                  List.of(
                      row.get("receipt_id") == null ? "" : row.get("receipt_id").toString(),
                      row.get("agreement_no") == null ? "" : row.get("agreement_no").toString(),
                      row.get("booking_no") == null ? "" : row.get("booking_no").toString(),
                      row.get("invoice_number") == null ? "" : row.get("invoice_number").toString(),
                      amountStr,
                      row.get("paid_through") == null ? "" : row.get("paid_through").toString(),
                      row.get("success_factor_id") == null
                          ? ""
                          : row.get("success_factor_id").toString(),
                      displayRegisterId,
                      row.get("branch_code") == null ? "" : row.get("branch_code").toString(),
                      row.get("transaction_date") == null
                          ? ""
                          : row.get("transaction_date").toString())));
          writer.write("\n");
        }
      }

      log.info("Monthly cash CSV created successfully: {}", fileName);
      return csvFile;

    } catch (Exception e) {
      log.error("Error in MonthlyCashReport job: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to generate monthly cash report", e);
    }
  }

  private String formatDate(Object date) {
    if (date == null) {
      return "";
    }
    if (date instanceof java.sql.Timestamp) {
      return ((java.sql.Timestamp) date)
          .toLocalDateTime()
          .atZone(ZoneId.of("UTC"))
          .withZoneSameInstant(SAUDI_ZONE)
          .format(DATE_FORMATTER);
    } else if (date instanceof LocalDateTime) {
      return ((LocalDateTime) date)
          .atZone(ZoneId.of("UTC"))
          .withZoneSameInstant(SAUDI_ZONE)
          .format(DATE_FORMATTER);
    }
    return date.toString().substring(0, 10);
  }

  private String getRegisterId(String branchCode, String registerId) {
    return String.format("%s%s", branchCode, StringUtils.leftPad(registerId.toString(), 5, '0'));
  }

  private java.io.File trafficFineReport() {
    String jobName = "TrafficFineReport";
    String CSV_HEADER = "TicketNo,Licence,Agreement,Status,InvoiceNo\n";
    log.info(" ***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query... for traffic fine report");
      String query =
          "SELECT ticket_number as TicketNo, "
              + "plate_no as Licence, "
              + "agreement_no as Agreement, "
              + "payment_status as status, "
              + "i.invoice_number as InvoiceNo "
              + "FROM `lumi-core-agreement-service`.agreement_traffic_fine atf "
              + "left join `lumi-core-agreement-service`.invoice i on atf.invoice_id = i.id";

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info(
          "Query executed successfully for traffic fine report. Found {} records", results.size());

      // Create CSV file
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_traffic_fine_report.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s\n",
                  row.get("TicketNo") == null ? "" : row.get("TicketNo"),
                  row.get("Licence") == null ? "" : row.get("Licence"),
                  row.get("Agreement") == null ? "" : row.get("Agreement"),
                  row.get("status") == null ? "" : row.get("status"),
                  row.get("InvoiceNo") == null ? "" : row.get("InvoiceNo")));
        }
      }
      log.info("CSV file created successfully for traffic fine report");

      return csvFile;

    } catch (Exception e) {
      log.error("Error in TrafficFineReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate traffic fine report", e);
    }
  }

  private java.io.File vehicleFleetStatusReport() {
    String jobName = "VehicleFleetStatusReport";
    String CSV_HEADER = "UNIT_NO,LICENSE_NO,ARABIC_LICENSE_NO,CHASSIS_NO,F_GROUP,Make_desc,Model_Desc,MODEL_YEAR,COLOR,Current_KM,FleetService,FLEET_SUB_ASSIGNMENT,STATUS_DESC,Current_Location,Last_Check_Out_Date,Last_Check_In_Date,purchase_date\n";
    log.info(" ***** Started Job {} *****", jobName);

    try {
      // Execute the query
      log.info("Executing database query... for vehicle fleet status report");
      String query =
          """
          select
              v.asset_id as UNIT_NO,
              v.plate_no as LICENSE_NO,
              v.plate_no_ar as ARABIC_LICENSE_NO,
              v.chassis_no as CHASSIS_NO,
              m.group_code as F_GROUP,
              mm.name_en as Make_desc,
              m.material_name as Model_Desc,
              v.year as MODEL_YEAR,
              v.color as COLOR,
              vod.odometer_reading as Current_KM,
              case vod.service_type_id
                  when 1 then 'RENTAL'
                  when 2 then 'LEASE'
                  when 3 then 'LIMOUSINE'
                  else 'UNKNOWN'
              end as FleetService,
              case vod.sub_service_type_id
                  when 1 then 'Rental Fleet'
                  when 2 then 'Lease Fleet'
                  when 3 then 'Allocation Awaiting'
                  when 4 then 'Towing Vehicles'
                  when 5 then 'Lease Return'
                  when 6 then 'Br Service Vehicle'
                  when 7 then 'Ws Maintenance'
                  when 8 then 'Mobile Service'
                  when 9 then 'Ws Accident'
                  when 10 then 'Ws Insurance'
                  when 11 then 'Dfs'
                  when 12 then 'Staff Use'
                  when 13 then 'Yard'
                  when 14 then 'Dfa'
                  when 15 then 'Dfi'
                  when 16 then 'Ws Awaiting'
                  when 17 then 'Chauffeur Fleet'
                  when 18 then 'Service Vehicle'
                  when 19 then 'Pre Lease'
                  when 20 then 'General Backup'
                  when 21 then 'Contract Backup'
                  when 22 then 'Foreign Vehicle'
                  else 'UNKNOWN'
              end as FLEET_SUB_ASSIGNMENT,
              case JSON_EXTRACT(vod.vehicle_status, '$.statusId')
                  when '1'  then 'Ready'
                  when '2'  then 'Rented'
                  when '13' then 'Need Preparation'
                  when '14' then 'NRM Opened'
                  when '6'  then 'Out Of Service'
                  when '15' then 'Disputed'
                  when '11' then 'Foreign Car Returned'
                  when '3'  then 'Stolen'
                  when '12' then 'Total Loss'
                  when '4'  then 'In Sale Cycle'
                  when '7'  then 'Sold'
                  when '16' then 'Staff Use Available'
                  when '17' then 'Backup'
                  when '18' then 'Transferred'
                  when '5'  then 'At Customer'
                  when '8'  then 'At Foreign Branch'
                  when '9'  then 'Reserved'
                  when '10' then 'Pre Checked In'
                  else 'UNKNOWN'
              end as STATUS_DESC,
              b.code as Current_Location,
              rev.checkout_date as Last_Check_Out_Date,
              rev.checkin_date as Last_Check_In_Date,
              vfi.purchase_date
          from `lumi-core-fleet-service`.vehicle v
          left join `lumi-core-fleet-service`.model m
              on v.model_id = m.id
          left join `lumi-core-fleet-service`.make mm
              on m.make_id = mm.id
          left join `lumi-core-fleet-service`.vehicle_operational_data vod
              on v.plate_no = vod.plate_no
          left join `lumi-core-fleet-service`.vehicle_financial_info vfi
              on v.plate_no = vfi.plate_no
          left join `branch_service`.branch b
              on vod.current_location_id = b.id
          LEFT JOIN (SELECT ir.plate_no,
                                       ir.checkin_date,
                                       ir.checkout_date
                                FROM   `lumi-core-fleet-service`.reservation ir
                                       INNER JOIN (SELECT plate_no, MAX(created_on) AS max_created_on
                                                   FROM   `lumi-core-fleet-service`.reservation
                                                   WHERE  plate_no IS NOT NULL
                                                   GROUP  BY plate_no
                                                   ) latest ON ir.plate_no = latest.plate_no AND ir.created_on = latest.max_created_on
                                                   )
                               rev
              ON v.plate_no = rev.plate_no 
              where b.is_yaqeen_migrated = true
          """;

      List<Map<String, Object>> results = jdbcTemplate.queryForList(query);
      log.info(
          "Query executed successfully for vehicle fleet status report. Found {} records", results.size());

      // Create CSV file
      String env =
          System.getenv("SPRING_PROFILES_ACTIVE") != null
              ? System.getenv("SPRING_PROFILES_ACTIVE")
              : "local";
      String fileName = env + "_vehicle_fleet_status_report.csv";
      java.io.File csvFile = new java.io.File(fileName);

      log.info("Creating CSV file: {}", fileName);
      try (FileWriter writer = new FileWriter(csvFile)) {
        writer.write(CSV_HEADER);
        for (Map<String, Object> row : results) {
          writer.write(
              String.format(
                  "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                  row.get("UNIT_NO") == null ? "" : row.get("UNIT_NO"),
                  row.get("LICENSE_NO") == null ? "" : row.get("LICENSE_NO"),
                  row.get("ARABIC_LICENSE_NO") == null ? "" : row.get("ARABIC_LICENSE_NO"),
                  row.get("CHASSIS_NO") == null ? "" : row.get("CHASSIS_NO"),
                  row.get("F_GROUP") == null ? "" : row.get("F_GROUP"),
                  row.get("Make_desc") == null ? "" : row.get("Make_desc"),
                  row.get("Model_Desc") == null ? "" : row.get("Model_Desc"),
                  row.get("MODEL_YEAR") == null ? "" : row.get("MODEL_YEAR"),
                  row.get("COLOR") == null ? "" : row.get("COLOR"),
                  row.get("Current_KM") == null ? "" : row.get("Current_KM"),
                  row.get("FleetService") == null ? "" : row.get("FleetService"),
                  row.get("FLEET_SUB_ASSIGNMENT") == null ? "" : row.get("FLEET_SUB_ASSIGNMENT"),
                  row.get("STATUS_DESC") == null ? "" : row.get("STATUS_DESC"),
                  row.get("Current_Location") == null ? "" : row.get("Current_Location"),
                  formatDate(row.get("Last_Check_Out_Date")),
                  formatDate(row.get("Last_Check_In_Date")),
                  formatDate(row.get("purchase_date"))));
        }
      }
      log.info("CSV file created successfully for vehicle fleet status report");
      return csvFile;

    } catch (Exception e) {
      log.error("Error in VehicleFleetStatusReport job: {}", e.getMessage(), e);
      if (e.getMessage().contains("accessNotConfigured")) {
        log.error("Google Drive API is not enabled. Please enable it in Google Cloud Console");
      }
      throw new RuntimeException("Failed to generate vehicle fleet status report", e);
    }
  }
}
