# Document Upload Integration

This document describes how to upload attachments using the existing fleet service document upload endpoint and get URLs in return.

## Overview

The system now integrates with your existing fleet service document upload endpoint to:
1. Upload generated reports and attachments
2. Return a list of URLs for the uploaded documents
3. Optionally send emails with attachments (existing functionality)

## Configuration

Add the following environment variables:

```bash
FLEET_SERVICE_BASE_URL=https://api.lumirental.com/core-fleet-service
FLEET_SERVICE_AUTH_TOKEN=your_auth_token_here
```

## API Endpoints

### 1. Upload Files Directly

Upload multiple files using the document upload service:

```bash
curl -X POST \
  http://localhost:8182/api/documents/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'plateNo=REPORT-123' \
  -F 'files=@file1.csv' \
  -F 'files=@file2.xlsx'
```

Response:
```json
{
  "success": true,
  "message": "Files uploaded successfully",
  "urls": [
    "response_from_fleet_service_1",
    "response_from_fleet_service_2"
  ],
  "uploadedCount": 2,
  "totalFiles": 2
}
```

### 2. Upload Single File

```bash
curl -X POST \
  http://localhost:8182/api/documents/upload-single \
  -H 'Content-Type: multipart/form-data' \
  -F 'plateNo=REPORT-123' \
  -F 'file=@report.csv'
```

### 3. Generate Reports and Upload

Generate custom reports and upload them to get URLs:

```bash
curl -X POST \
  http://localhost:8182/api/reports/generate-and-upload \
  -H 'Content-Type: application/json' \
  -d '{
    "reports": {
      "daily_report.csv": "SELECT * FROM agreements WHERE DATE(created_on) = CURDATE()",
      "monthly_summary.xlsx": "DAILY_CASH_EXCEL"
    },
    "to": "<EMAIL>",
    "subject": "Optional email subject",
    "body": "Optional email body"
  }'
```

Response:
```json
{
  "success": true,
  "message": "Reports generated and uploaded successfully",
  "urls": [
    "fleet_service_response_for_daily_report",
    "fleet_service_response_for_monthly_summary"
  ],
  "uploadedCount": 2,
  "requestedReports": 2
}
```

### 4. Check Service Status

```bash
curl -X GET http://localhost:8182/api/documents/status
```

Response:
```json
{
  "configured": true,
  "message": "Document upload service is configured and ready"
}
```

## How It Works

### Document Upload Service

The `DocumentUploadService` integrates with your existing fleet service endpoint:
- **Endpoint**: `POST /v1/vehicles/documents/upload`
- **Parameters**: `plateNo` and `file`
- **Authentication**: Bearer token via `FLEET_SERVICE_AUTH_TOKEN`

### Report Generation and Upload

1. **Generate Reports**: Creates CSV/Excel files based on SQL queries
2. **Upload to Fleet Service**: Uses the document upload endpoint
3. **Return URLs**: Returns the responses from the fleet service
4. **Optional Email**: Can still send emails with attachments if requested
5. **Cleanup**: Automatically deletes temporary files

### Plate Number Handling

- If `plateNo` is provided, it's used directly
- If not provided, a default plate number is generated: `REPORT-{timestamp}`
- This ensures compatibility with the fleet service requirement

## Integration with Existing Code

The system maintains backward compatibility:

- `generateAndSendCustomReports()` - Original email functionality
- `generateAndUploadCustomReports()` - New upload functionality that returns URLs

## Error Handling

- Service checks if fleet service is configured
- Handles authentication errors
- Logs all upload attempts and results
- Returns appropriate HTTP status codes
- Cleans up temporary files even on errors

## Example Usage in Code

```java
// Generate reports and get URLs
CustomReportRequest request = new CustomReportRequest();
request.setReports(Map.of(
    "report.csv", "SELECT * FROM table",
    "excel.xlsx", "DAILY_CASH_EXCEL"
));

List<String> urls = reportScheduler.generateAndUploadCustomReports(request);
// urls contains the responses from fleet service
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLEET_SERVICE_BASE_URL` | Fleet service base URL | `https://api.lumirental.com/core-fleet-service` |
| `FLEET_SERVICE_AUTH_TOKEN` | Authentication token for fleet service | (required) |

## Testing

1. Set the environment variables
2. Start the application
3. Check service status: `GET /api/documents/status`
4. Upload a test file: `POST /api/documents/upload-single`
5. Generate and upload reports: `POST /api/reports/generate-and-upload`
