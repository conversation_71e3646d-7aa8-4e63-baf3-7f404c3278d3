package com.seera.lumi.yaqeen.repository;

import com.seera.lumi.yaqeen.domain.Maintenance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaintenanceRepository extends JpaRepository<Maintenance, Long> {
    /**
     * Find all maintenance records by plate number ordered by document date descending
     * @param plateNo the plate number to search for
     * @return list of maintenance records for the given plate ordered by document date desc
     */
    List<Maintenance> findByPlateNoOrderByDocumentDateDesc(String plateNo);
}
